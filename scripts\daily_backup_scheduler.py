#!/usr/bin/env python3
"""
Daily Backup Scheduler for TNGD

This module provides automated daily backup functionality with:
- Sequential table processing (one at a time)
- Comprehensive error handling with exponential backoff retry
- Email notifications with detailed summaries
- Automatic cleanup and validation
- Performance monitoring and reporting

Features:
- Reads tables from table.json configuration
- Processes tables sequentially to minimize resource usage
- Implements chunked data extraction for large tables
- Compresses backups using TAR.GZ format
- Stores in OSS with configurable path structure: Devo/{month}/week {n}/{date}/
- Sends email summary upon completion
- Includes retry mechanism for failed backups
- Validates backup integrity
- Cleans up temporary files immediately

Usage:
    python scripts/daily_backup_scheduler.py [options]

Options:
    --dry-run          Validate tables only, no backup
    --single-table     Process only one table for testing
    --force-email      Send email even in test mode
    --verbose          Enable verbose logging
"""

import os
import sys
import argparse
import logging
import time
import datetime
import json
import re
from typing import Dict, Any, List, Optional, Union
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import project modules
from core.backup_config import BackupConfig
from core.config_manager import ConfigManager
from core.unified_table_processor import UnifiedTableProcessor
from utils.minimal_logging import logger
from utils.notification import send_backup_summary_notification
from utils.disk_cleanup import cleanup_temp_files

class DailyBackupScheduler:
    """
    Daily backup scheduler that handles automated daily backups with
    comprehensive error handling, monitoring, and reporting.
    """

    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        Initialize the daily backup scheduler.

        Args:
            config_manager: Configuration manager instance
        """
        self.config_manager = config_manager or ConfigManager()
        self.start_time = datetime.datetime.now()
        self.stats = {
            'total_tables': 0,
            'successful_tables': 0,
            'failed_tables': 0,
            'empty_tables_skipped': 0,
            'total_rows_backed_up': 0,
            'total_processing_time': 0,
            'failed_table_details': {},
            'performance_metrics': {}
        }
        self.backup_date = None  # Will be set from main
        self.args = None  # Will be set from main

    def load_table_list(self) -> List[str]:
        """
        Load table names from tabletest/tables.json.

        Returns:
            List of table names
        """
        try:
            tables = self.config_manager.get_tables_from_file()
            if not tables:
                logger.error("No tables found in tabletest/tables.json")
                return []

            logger.info(f"Loaded {len(tables)} tables from configuration")
            return tables

        except Exception as e:
            logger.error(f"Error loading table list: {str(e)}")
            return []

    def create_backup_config(self, args) -> BackupConfig:
        """
        Create backup configuration optimized for daily backups.

        Args:
            args: Command line arguments

        Returns:
            BackupConfig object
        """
        # Use from_args to support --date and all CLI overrides
        config = BackupConfig.from_args(args, config_manager=self.config_manager)
        return config

    def run_daily_backup(self, args) -> Dict[str, Any]:
        """
        Execute the daily backup process.

        Args:
            args: Command line arguments

        Returns:
            Dictionary with backup results
        """
        logger.log_structured(level="INFO", component="DailyBackupScheduler", phase="INIT", message="Starting automated daily backup process")

        # Load table list
        table_names = self.load_table_list()
        if not table_names:
            logger.log_structured(level="ERROR", component="DailyBackupScheduler", phase="ERROR", message="No tables to backup")
            return {
                'status': 'error',
                'error': 'No tables to backup',
                'stats': self.stats
            }

        self.stats['total_tables'] = len(table_names)
        logger.log_structured(level="INFO", component="DailyBackupScheduler", phase="PROCESS", message=f"Processing {len(table_names)} tables sequentially")

        # Create backup configuration (now supports --date)
        config = self.create_backup_config(args)

        try:
            if args.dry_run:
                return self._run_dry_run_validation(table_names)
            elif args.single_table:
                return self._run_single_table_test(table_names, config)
            else:
                return self._run_full_backup(table_names, config)

        except Exception as e:
            logger.log_structured(level="ERROR", component="DailyBackupScheduler", phase="ERROR", message=f"Daily backup failed with error: {str(e)}")
            return {
                'status': 'error',
                'error': str(e),
                'stats': self.stats
            }

    def _run_dry_run_validation(self, table_names: List[str]) -> Dict[str, Any]:
        """
        Run dry-run validation of all tables.

        Args:
            table_names: List of table names to validate

        Returns:
            Validation results
        """
        logger.log_structured(level="INFO", component="DailyBackupScheduler", phase="PROCESS", message="Running dry-run validation (no actual backup)")

        # Use unified table processor for validation
        config = BackupConfig()
        processor = UnifiedTableProcessor(config, strategy='smart')

        validation_stats = {
            'accessible': 0,
            'with_data': 0,
            'empty': 0,
            'inaccessible': 0
        }

        for table_name in table_names:
            try:
                # This would validate the table without backing up
                logger.log_structured(level="INFO", component="DailyBackupScheduler", phase="PROCESS", message=f"Validating table: {table_name}")
                # Add validation logic here
                validation_stats['accessible'] += 1
                validation_stats['with_data'] += 1

            except Exception as e:
                logger.log_structured(level="ERROR", component="DailyBackupScheduler", phase="ERROR", message=f"Validation failed for {table_name}: {str(e)}")
                validation_stats['inaccessible'] += 1

        logger.log_structured(level="INFO", component="DailyBackupScheduler", phase="SUCCESS", message=f"Validation complete: {validation_stats}")
        return {
            'status': 'success',
            'mode': 'dry_run',
            'validation_stats': validation_stats,
            'stats': self.stats
        }

    def _run_single_table_test(self, table_names: List[str], config: BackupConfig) -> Dict[str, Any]:
        """
        Run backup on a single table for testing.

        Args:
            table_names: List of table names
            config: Backup configuration

        Returns:
            Test results
        """
        test_table = table_names[0] if table_names else "my.app.tngd.waf"
        logger.log_structured(level="INFO", component="DailyBackupScheduler", phase="PROCESS", message=f"Running single table test with: {test_table}")

        # Use unified table processor for single table
        processor = UnifiedTableProcessor(config, strategy='smart')
        result = processor.process_tables([test_table], skip_empty=False)

        return {
            'status': 'success',
            'mode': 'single_table_test',
            'test_table': test_table,
            'result': result,
            'stats': self.stats
        }

    def _run_full_backup(self, table_names: List[str], config: BackupConfig) -> Dict[str, Any]:
        """
        Run full backup of all tables.

        Args:
            table_names: List of table names
            config: Backup configuration

        Returns:
            Backup results
        """
        logger.log_structured(level="INFO", component="DailyBackupScheduler", phase="PROCESS", message="Starting full daily backup process")

        # Use unified table processor for sequential processing
        processor = UnifiedTableProcessor(config, strategy='smart')

        # Process tables one by one (skip empty tables by default)
        result = processor.process_tables(table_names, skip_empty=True)

        # Update stats
        summary = result.get('summary', {})
        self.stats.update({
            'successful_tables': summary.get('successful_backups', 0),
            'failed_tables': summary.get('failed_backups', 0),
            'empty_tables_skipped': summary.get('empty_tables', 0),
            'total_rows_backed_up': summary.get('total_rows_backed_up', 0),
            'total_processing_time': summary.get('total_processing_time', 0)
        })

        return {
            'status': 'success' if summary.get('failed_backups', 0) == 0 else 'partial',
            'mode': 'full_backup',
            'result': result,
            'stats': self.stats
        }

    def send_completion_notification(self, backup_result: Union[Dict[str, Any], str, None], force_email: bool = False):
        """
        Send email notification about backup completion.

        Args:
            backup_result: Results from backup process (can be dict, string, or None)
            force_email: Force sending email even in test mode
        """
        try:
            # Check if notifications are enabled
            notification_config = self.config_manager.get('notification', 'daily_backup', {})
            if not notification_config.get('enabled', True) and not force_email:
                logger.info("Daily backup notifications are disabled")
                return

            # Prepare summary for email
            end_time = datetime.datetime.now()
            duration = (end_time - self.start_time).total_seconds()

            # SECURITY FIX: Defensive programming - validate backup_result type before accessing
            # Handle cases where backup_result might be a string (error message) or None
            backup_results_data = {}
            if backup_result is None:
                logger.warning("backup_result is None, using empty results for notification")
                backup_results_data = {}
            elif isinstance(backup_result, dict):
                # Normal case: backup_result is a dictionary
                backup_results_data = backup_result.get('result', {})
                logger.info("backup_result is dict, extracted results successfully")
            elif isinstance(backup_result, str):
                # Error case: backup_result is an error message string
                logger.warning(f"backup_result is string (likely error): {backup_result[:100]}...")
                backup_results_data = {}
            else:
                # Unexpected type: log warning and use empty results
                logger.warning(f"backup_result has unexpected type {type(backup_result)}, using empty results")
                backup_results_data = {}

            summary = {
                'start_time': self.start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration_seconds': duration,
                'total_tables': self.stats['total_tables'],
                'successful_tables': self.stats['successful_tables'],
                'failed_tables': self.stats['failed_tables'],
                'total_rows_backed_up': self.stats['total_rows_backed_up'],
                'results': backup_results_data,  # Use validated results data
                'performance': self.stats.get('performance_metrics', {})
            }

            # Send notification
            success = send_backup_summary_notification(summary, self.config_manager)
            if success:
                logger.info("Email notification sent successfully")
            else:
                logger.warning("Failed to send email notification")

        except Exception as e:
            logger.error(f"Error sending notification: {str(e)}")
            # Additional context logging for debugging notification issues
            logger.error(f"backup_result type: {type(backup_result)}, stats: {self.stats}")

    def cleanup_after_backup(self):
        """Perform cleanup operations after backup completion."""
        try:
            logger.log_structured(level="INFO", component="DailyBackupScheduler", phase="PROCESS", message="Performing post-backup cleanup")

            # Clean up temporary files
            cleanup_temp_files(force=True)

            # Additional cleanup operations can be added here
            logger.log_structured(level="INFO", component="DailyBackupScheduler", phase="SUCCESS", message="Post-backup cleanup completed")

        except Exception as e:
            logger.log_structured(level="ERROR", component="DailyBackupScheduler", phase="ERROR", message=f"Error during cleanup: {str(e)}")
            # Added pass to satisfy try-except block
            pass

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description='Daily Backup Scheduler for TNGD',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )

    parser.add_argument('table', nargs='?',
                       help='Specific table to backup (optional)')
    parser.add_argument('--dry-run', action='store_true',
                       help='Validate tables only, do not backup')
    parser.add_argument('--single-table', action='store_true',
                       help='Process only one table for testing')
    parser.add_argument('--force-email', action='store_true',
                       help='Send email notification even in test mode')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    parser.add_argument('--chunk-size', type=int,
                       help='Override default chunk size')
    parser.add_argument('--timeout', type=int,
                       help='Override default timeout')
    parser.add_argument('--date', type=str, default=None,
                       help='Backup for a specific date (YYYY-MM-DD). Overrides --days.')
    parser.add_argument('--days', type=int, default=1,
                       help='Number of days to back up (default: 1)')

    # Add all arguments expected by BackupConfig.from_args, default=None, hidden from help
    parser.add_argument('--max-retries', type=int, default=None, help=argparse.SUPPRESS)
    parser.add_argument('--max-threads', type=int, default=None, help=argparse.SUPPRESS)
    parser.add_argument('--max-concurrent-tables', type=int, default=None, help=argparse.SUPPRESS)
    parser.add_argument('--batch-size', type=int, default=None, help=argparse.SUPPRESS)
    parser.add_argument('--table-timeout', type=int, default=None, help=argparse.SUPPRESS)
    parser.add_argument('--tables', type=str, default=None, help=argparse.SUPPRESS)
    parser.add_argument('--mode', type=str, default=None, help=argparse.SUPPRESS)
    parser.add_argument('--month', type=int, default=None, help=argparse.SUPPRESS)
    parser.add_argument('--year', type=int, default=None, help=argparse.SUPPRESS)
    parser.add_argument('--chunking-strategy', type=str, default=None, help=argparse.SUPPRESS)
    parser.add_argument('--monthly-max-retries', type=int, default=None, help=argparse.SUPPRESS)
    parser.add_argument('--monthly-retry-delay', type=int, default=None, help=argparse.SUPPRESS)

    return parser.parse_args()

def main():
    """Main function for daily backup scheduler."""
    args = parse_arguments()

    # Determine backup date for logging
    backup_date_str = None
    if args.date:
        # Validate date format
        date_pattern = r"^\\d{4}-\\d{2}-\\d{2}$"
        if not re.match(date_pattern, args.date):
            print(f"[ERROR] Invalid date format: {args.date}. Expected YYYY-MM-DD.")
            return 1
        backup_date_str = args.date
    else:
        backup_date_str = datetime.datetime.now().strftime('%Y-%m-%d')

    # Patch logger to use date-specific log file
    from utils import minimal_logging
    log_dir = minimal_logging.DEFAULT_LOG_DIR
    log_file = f"backup_{backup_date_str}.log"
    log_path = os.path.join(log_dir, log_file)
    for handler in minimal_logging.logger._logger.handlers[:]:
        if isinstance(handler, logging.handlers.RotatingFileHandler):
            minimal_logging.logger._logger.removeHandler(handler)
    file_handler = logging.handlers.RotatingFileHandler(
        log_path,
        maxBytes=minimal_logging.DEFAULT_MAX_SIZE_MB * 1024 * 1024,
        backupCount=minimal_logging.DEFAULT_MAX_FILES,
        encoding='utf-8'
    )
    file_handler.setFormatter(
        logging.Formatter('%(asctime)s [%(levelname)s] %(message)s', 
                        datefmt='%Y-%m-%d %H:%M:%S')
    )
    minimal_logging.logger._logger.addHandler(file_handler)
    minimal_logging.logger._logger.info(f"Log file: {log_path}")

    try:
        # Log startup
        logger.log_operation("DAILY BACKUP SCHEDULER", "STARTED", 
                           f"Started at: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # Create and run daily backup scheduler
        scheduler = DailyBackupScheduler()
        # Attach date to args for downstream logic
        scheduler.backup_date = backup_date_str
        scheduler.args = args
        backup_result = scheduler.run_daily_backup(args)

        # Send notification if not dry run or if forced
        if not args.dry_run or args.force_email:
            scheduler.send_completion_notification(backup_result, args.force_email)

        # Cleanup after backup
        if not args.dry_run:
            scheduler.cleanup_after_backup()

        # Return appropriate exit code
        if backup_result['status'] == 'error':
            logger.error("Daily backup failed")
            return 1
        elif backup_result['status'] == 'partial':
            logger.warning("Daily backup completed with some failures")
            return 1
        else:
            logger.log_structured(level="INFO", component="DailyBackupScheduler", phase="SUCCESS", message="Daily backup completed successfully")
            return 0

    except KeyboardInterrupt:
        logger.log_structured(level="WARNING", component="DailyBackupScheduler", phase="ERROR", message="Daily backup interrupted by user")
        return 1
    except Exception as e:
        logger.log_structured(level="ERROR", component="DailyBackupScheduler", phase="ERROR", message=f"Unexpected error in daily backup: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
